import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router';
import { Button } from '@repo/ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@repo/ui/components/card';
import { Progress } from '@repo/ui/components/progress';
import { Skeleton } from '@repo/ui/components/skeleton';
import { Alert, AlertDescription } from '@repo/ui/components/alert';
import { Badge } from '@repo/ui/components/badge';
import { USER_ROUTES } from '@/app.routes';
import {
  getEngagementSurveyData,
  saveQuestionResponse,
  saveCommentResponse,
  submitSurvey,
  type EngagementSurveyData,
  type SurveySection
} from '@/services/engagementSurveyService';
import QuestionRenderer from '@/components/survey/questions/QuestionRenderer';

// Remove duplicate interfaces since they're imported from the service

const EngagementSurvey: React.FC = () => {
  const { surveyId } = useParams<{ surveyId: string }>();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [surveyData, setSurveyData] = useState<EngagementSurveyData | null>(null);
  const [currentSection, setCurrentSection] = useState<SurveySection | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [confirmSubmit, setConfirmSubmit] = useState(false);
  const [saveTimeout, setSaveTimeout] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (surveyId) {
      fetchSurveyData();
    }
  }, [surveyId]);

  const fetchSurveyData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      if (!surveyId) {
        throw new Error('Survey ID is required');
      }

      const data = await getEngagementSurveyData(surveyId);
      setSurveyData(data);

      // Set the first section as current
      if (data.sections.length > 0) {
        setCurrentSection(data.sections[0]);
      }
    } catch (err) {
      console.error('Error fetching survey data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load survey data');
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-save functionality with debouncing
  const debouncedSave = (saveFunction: () => Promise<void>) => {
    if (saveTimeout) {
      clearTimeout(saveTimeout);
    }

    const timeout = setTimeout(async () => {
      try {
        setIsSaving(true);
        await saveFunction();
      } catch (err) {
        console.error('Error saving response:', err);
      } finally {
        setIsSaving(false);
      }
    }, 1000); // 1 second debounce

    setSaveTimeout(timeout);
  };

  const handleResponseUpdate = (questionId: number, value: any, responseId?: number) => {
    if (!surveyData) return;

    debouncedSave(async () => {
      await saveQuestionResponse(surveyData.responseId, questionId, value, responseId);
      // Optionally refresh survey data to get updated completion percentage
      await fetchSurveyData();
    });
  };

  const handleCommentUpdate = (questionId: number, comment: string, commentId?: number) => {
    if (!surveyData) return;

    debouncedSave(async () => {
      await saveCommentResponse(surveyData.responseId, questionId, comment, commentId);
    });
  };

  const handleNext = () => {
    if (!currentSection || !surveyData) return;

    const currentIndex = surveyData.sections.findIndex(s => s.id === currentSection.id);
    if (currentIndex < surveyData.sections.length - 1) {
      setCurrentSection(surveyData.sections[currentIndex + 1]);
    } else if (surveyData.meta.canSubmit) {
      setConfirmSubmit(true);
    }
  };

  const handleBack = () => {
    if (!currentSection || !surveyData) return;

    const currentIndex = surveyData.sections.findIndex(s => s.id === currentSection.id);
    if (currentIndex > 0) {
      setCurrentSection(surveyData.sections[currentIndex - 1]);
    }
  };

  const handleSubmit = async () => {
    try {
      setIsSaving(true);
      if (surveyData) {
        await submitSurvey(surveyData.responseId);
        navigate(USER_ROUTES().dashboard.surveyList);
      }
    } catch (err) {
      console.error('Error submitting survey:', err);
      setError('Failed to submit survey');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSectionSelect = (section: SurveySection) => {
    setCurrentSection(section);
    setConfirmSubmit(false);
  };

  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (confirmSubmit) {
    return (
      <div className="min-h-screen flex items-center justify-center p-6">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Submit Survey</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>Are you ready to submit your responses?</p>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={() => setConfirmSubmit(false)}
                className="flex-1"
              >
                Back
              </Button>
              <Button 
                onClick={handleSubmit}
                disabled={isSaving}
                className="flex-1"
              >
                {isSaving ? 'Submitting...' : 'Submit'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <Button 
                variant="ghost" 
                onClick={() => navigate(USER_ROUTES().dashboard.surveyList)}
                className="mb-2"
              >
                ← Back to Surveys
              </Button>
              <h1 className="text-2xl font-bold">
                {isLoading ? <Skeleton className="h-8 w-64" /> : surveyData?.meta.title}
              </h1>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">Progress</p>
              <p className="font-semibold">{surveyData?.completion || 0}% Complete</p>
            </div>
          </div>
          <Progress value={surveyData?.completion || 0} className="mt-4" />
        </div>
      </div>

      <div className="max-w-7xl mx-auto flex">
        {/* Sidebar */}
        <div className="w-80 bg-white border-r min-h-screen p-6">
          <div className="space-y-6">
            {/* Survey Info */}
            <div>
              <h3 className="font-semibold mb-2">Survey Ends On</h3>
              <p className="text-sm text-gray-600">
                {isLoading ? <Skeleton className="h-4 w-32" /> : surveyData?.meta.endDate}
              </p>
            </div>

            {/* Progress */}
            <div>
              <h3 className="font-semibold mb-2">Survey Status</h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Questions</span>
                  <span>{surveyData?.questions.length || 0}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Completed</span>
                  <span>{Math.round(((surveyData?.completion || 0) / 100) * (surveyData?.questions.length || 0))}</span>
                </div>
              </div>
            </div>

            {/* Sections */}
            <div>
              <h3 className="font-semibold mb-2">Categories</h3>
              <div className="space-y-1">
                {isLoading ? (
                  Array(3).fill(0).map((_, index) => (
                    <Skeleton key={index} className="h-8 w-full" />
                  ))
                ) : (
                  surveyData?.sections.map((section) => {
                    // Check if section is completed (all questions in section have responses)
                    const sectionQuestions = section.questions;
                    const completedQuestions = sectionQuestions.filter(q =>
                      q.response?.value !== undefined && q.response?.value !== null && q.response?.value !== ''
                    );
                    const isCompleted = sectionQuestions.length > 0 && completedQuestions.length === sectionQuestions.length;

                    return (
                      <Button
                        key={section.id}
                        variant={currentSection?.id === section.id ? 'default' : 'ghost'}
                        className="w-full justify-start"
                        onClick={() => handleSectionSelect(section)}
                      >
                        <span className="flex-1 text-left">{section.title}</span>
                        {isCompleted && (
                          <Badge variant="secondary" className="ml-2">✓</Badge>
                        )}
                      </Button>
                    );
                  })
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6">
          {isLoading ? (
            <div className="space-y-6">
              <Skeleton className="h-8 w-3/4" />
              <Skeleton className="h-32 w-full" />
              <Skeleton className="h-32 w-full" />
            </div>
          ) : (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold">{currentSection?.title}</h2>

              {/* Render questions for current section */}
              {currentSection?.questions.map((question, index) => (
                <QuestionRenderer
                  key={question.id}
                  question={question}
                  questionNumber={index + 1}
                  onResponseUpdate={handleResponseUpdate}
                  onCommentUpdate={handleCommentUpdate}
                  isSaving={isSaving}
                />
              ))}

              {/* Navigation */}
              <div className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={handleBack}
                  disabled={surveyData?.meta.hideBack}
                >
                  Back
                </Button>
                <Button onClick={handleNext}>
                  {surveyData?.sections && currentSection &&
                   surveyData.sections.findIndex(s => s.id === currentSection.id) === surveyData.sections.length - 1
                    ? (surveyData.meta.canSubmit ? 'Submit Survey' : 'Complete Required Questions')
                    : (surveyData?.meta.nextTitle || 'Next')
                  }
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EngagementSurvey;
