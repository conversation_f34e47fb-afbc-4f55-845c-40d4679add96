import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router';
import { Button } from '@repo/ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@repo/ui/components/card';
import { Progress } from '@repo/ui/components/progress';
import { Skeleton } from '@repo/ui/components/skeleton';
import { Alert, AlertDescription } from '@repo/ui/components/alert';
import { Badge } from '@repo/ui/components/badge';
import { USER_ROUTES } from '@/app.routes';
import {
  getEngagementSurveyData,
  saveQuestionResponse,
  saveCommentResponse,
  submitSurvey,
  type EngagementSurveyData,
  type SurveySection
} from '@/services/engagementSurveyService';
import QuestionRenderer from '@/components/survey/questions/QuestionRenderer';

// Remove duplicate interfaces since they're imported from the service

const EngagementSurvey: React.FC = () => {
  const { surveyId } = useParams<{ surveyId: string }>();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [surveyData, setSurveyData] = useState<EngagementSurveyData | null>(null);
  const [currentSection, setCurrentSection] = useState<SurveySection | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [confirmSubmit, setConfirmSubmit] = useState(false);
  const [saveTimeout, setSaveTimeout] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (surveyId) {
      fetchSurveyData();
    }
  }, [surveyId]);

  const fetchSurveyData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      if (!surveyId) {
        throw new Error('Survey ID is required');
      }

      const data = await getEngagementSurveyData(surveyId);
      setSurveyData(data);

      // Set the first section as current
      if (data.sections.length > 0) {
        setCurrentSection(data.sections[0]);
      }
    } catch (err) {
      console.error('Error fetching survey data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load survey data');
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-save functionality with debouncing
  const debouncedSave = (saveFunction: () => Promise<void>) => {
    if (saveTimeout) {
      clearTimeout(saveTimeout);
    }

    const timeout = setTimeout(async () => {
      try {
        setIsSaving(true);
        await saveFunction();
      } catch (err) {
        console.error('Error saving response:', err);
      } finally {
        setIsSaving(false);
      }
    }, 1000); // 1 second debounce

    setSaveTimeout(timeout);
  };

  const handleResponseUpdate = (questionId: number, value: any, responseId?: number) => {
    if (!surveyData) return;

    debouncedSave(async () => {
      await saveQuestionResponse(surveyData.responseId, questionId, value, responseId);
      // Optionally refresh survey data to get updated completion percentage
      await fetchSurveyData();
    });
  };

  const handleCommentUpdate = (questionId: number, comment: string, commentId?: number) => {
    if (!surveyData) return;

    debouncedSave(async () => {
      await saveCommentResponse(surveyData.responseId, questionId, comment, commentId);
    });
  };

  const handleNext = () => {
    if (!currentSection || !surveyData) return;

    const currentIndex = surveyData.sections.findIndex(s => s.id === currentSection.id);
    if (currentIndex < surveyData.sections.length - 1) {
      setCurrentSection(surveyData.sections[currentIndex + 1]);
    } else if (surveyData.meta.canSubmit) {
      setConfirmSubmit(true);
    }
  };

  const handleBack = () => {
    if (!currentSection || !surveyData) return;

    const currentIndex = surveyData.sections.findIndex(s => s.id === currentSection.id);
    if (currentIndex > 0) {
      setCurrentSection(surveyData.sections[currentIndex - 1]);
    }
  };

  const handleSubmit = async () => {
    try {
      setIsSaving(true);
      if (surveyData) {
        await submitSurvey(surveyData.responseId);
        navigate(USER_ROUTES().dashboard.surveyList);
      }
    } catch (err) {
      console.error('Error submitting survey:', err);
      setError('Failed to submit survey');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSectionSelect = (section: SurveySection) => {
    setCurrentSection(section);
    setConfirmSubmit(false);
  };

  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (confirmSubmit) {
    return (
      <div className="min-h-screen flex items-center justify-center p-6">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Submit Survey</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>Are you ready to submit your responses?</p>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={() => setConfirmSubmit(false)}
                className="flex-1"
              >
                Back
              </Button>
              <Button 
                onClick={handleSubmit}
                disabled={isSaving}
                className="flex-1"
              >
                {isSaving ? 'Submitting...' : 'Submit'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header - matches legacy design */}
      <div className="bg-blue-500 text-white px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              onClick={() => navigate(USER_ROUTES().dashboard.surveyList)}
              className="text-white hover:bg-blue-600 p-1"
            >
              ← Back
            </Button>
            <h1 className="text-xl font-medium">
              {isLoading ? <Skeleton className="h-6 w-64 bg-blue-400" /> : surveyData?.meta.title}
            </h1>
          </div>
          <div className="text-right">
            <span className="text-sm">
              {surveyData?.completion || 0}% Complete
            </span>
            <span className="ml-4 text-sm">
              Saved {new Date().toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </span>
          </div>
        </div>
      </div>

      <div className="flex">
        {/* Left Sidebar - matches legacy design */}
        <div className="w-48 bg-gray-100 min-h-screen p-4 border-r">
          {/* Survey End Date */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-700 mb-1">Survey Ends On</h3>
            <p className="text-sm text-gray-600">
              {isLoading ? <Skeleton className="h-4 w-32" /> :
                surveyData?.meta.endDate ?
                  new Date(surveyData.meta.endDate).toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  }) : 'Not set'
              }
            </p>
          </div>

          {/* Survey Status */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Survey Status</h3>
            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">{surveyData?.questions.length || 0}</span>
                <span className="text-gray-500">Questions</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-blue-600 font-medium">
                  {Math.round(((surveyData?.completion || 0) / 100) * (surveyData?.questions.length || 0))}
                </span>
                <span className="text-gray-500">Completed</span>
              </div>
            </div>
          </div>

          {/* Categories */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-2">Categories</h3>
            <div className="space-y-1">
              {isLoading ? (
                Array(3).fill(0).map((_, index) => (
                  <Skeleton key={index} className="h-6 w-full" />
                ))
              ) : (
                surveyData?.sections.map((section) => {
                  const sectionQuestions = section.questions;
                  const completedQuestions = sectionQuestions.filter(q =>
                    q.response?.value !== undefined && q.response?.value !== null && q.response?.value !== ''
                  );
                  const questionsLeft = sectionQuestions.length - completedQuestions.length;

                  return (
                    <div
                      key={section.id}
                      className={`p-2 rounded cursor-pointer text-sm ${
                        currentSection?.id === section.id
                          ? 'bg-blue-100 text-blue-800 border border-blue-200'
                          : 'hover:bg-gray-200 text-gray-700'
                      }`}
                      onClick={() => handleSectionSelect(section)}
                    >
                      <div className="font-medium">{section.title}</div>
                      {questionsLeft > 0 && (
                        <div className="text-xs text-gray-500 mt-1">
                          {questionsLeft} questions left
                        </div>
                      )}
                    </div>
                  );
                })
              )}
            </div>
          </div>
        </div>

        {/* Main Content Area - matches legacy design */}
        <div className="flex-1 bg-white">
          {isLoading ? (
            <div className="p-6 space-y-6">
              <Skeleton className="h-8 w-3/4" />
              <Skeleton className="h-32 w-full" />
              <Skeleton className="h-32 w-full" />
            </div>
          ) : (
            <div className="p-6">
              <h2 className="text-lg font-medium mb-6 text-gray-800">{currentSection?.title}</h2>

              {/* Render questions for current section */}
              <div className="space-y-6">
                {currentSection?.questions.map((question, index) => (
                  <QuestionRenderer
                    key={question.id}
                    question={question}
                    questionNumber={index + 1}
                    onResponseUpdate={handleResponseUpdate}
                    onCommentUpdate={handleCommentUpdate}
                    isSaving={isSaving}
                  />
                ))}
              </div>

              {/* Show remaining questions in section */}
              {currentSection && currentSection.questions.length > 0 && (
                <div className="mt-8 p-4 bg-gray-50 rounded text-center">
                  <p className="text-sm text-gray-600">
                    {currentSection.questions.filter(q =>
                      q.response?.value === undefined || q.response?.value === null || q.response?.value === ''
                    ).length} Questions left in this section
                  </p>
                  <Button
                    onClick={handleNext}
                    className="mt-4 bg-blue-600 hover:bg-blue-700"
                  >
                    {surveyData?.sections && currentSection &&
                     surveyData.sections.findIndex(s => s.id === currentSection.id) === surveyData.sections.length - 1
                      ? 'Submit my response'
                      : 'Next'
                    }
                  </Button>
                </div>
              )}

              {/* Navigation */}
              <div className="flex justify-between mt-8 pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={handleBack}
                  disabled={surveyData?.meta.hideBack}
                >
                  Back
                </Button>
                <Button
                  onClick={handleNext}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EngagementSurvey;
