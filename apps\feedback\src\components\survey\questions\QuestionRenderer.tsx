import React, { useState, useEffect } from 'react';
import { Button } from '@repo/ui/components/button';
import { Textarea } from '@repo/ui/components/textarea';
import { Input } from '@repo/ui/components/input';
import { RadioGroup, RadioGroupItem } from '@repo/ui/components/radio-group';
import { Checkbox } from '@repo/ui/components/checkbox';
import { Label } from '@repo/ui/components/label';
import { MessageSquare } from 'lucide-react';
import { SurveyQuestion } from '@/services/engagementSurveyService';

interface QuestionRendererProps {
  question: SurveyQuestion;
  questionNumber: number;
  onResponseUpdate: (questionId: string, value: any, responseId?: string) => void;
  onCommentUpdate: (questionId: string, comment: string, commentId?: string) => void;
  isSaving: boolean;
}

const QuestionRenderer: React.FC<QuestionRendererProps> = ({
  question,
  questionNumber,
  onResponseUpdate,
  onCommentUpdate,
  isSaving
}) => {
  const [response, setResponse] = useState(question.response?.value || '');
  const [feedback, setFeedback] = useState('');
  const [showFeedback, setShowFeedback] = useState(false);

  useEffect(() => {
    setResponse(question.response?.value || '');
  }, [question.response?.value]);

  const handleResponseChange = (newResponse: any) => {
    setResponse(newResponse);
    onResponseUpdate(question.id, newResponse, question.response?.id);
  };

  const handleFeedbackChange = (newFeedback: string) => {
    setFeedback(newFeedback);
    onCommentUpdate(question.id, newFeedback);
  };

  const renderRatingScale = () => {
    const ratings = [
      { value: 5, label: 'Scale - 5' },
      { value: 4, label: 'Scale - 4' },
      { value: 3, label: 'Scale - 3' },
      { value: 2, label: 'Scale - 2' },
      { value: 1, label: 'Scale - 1' },
      { value: 0, label: 'No basis for rating' }
    ];

    // If reverse scale is enabled, reverse the order
    const displayRatings = question.is_reverse_scale ? [...ratings].reverse() : ratings;

    return (
      <div className="bg-gray-50 p-4 rounded">
        <div className="flex justify-between items-center">
          {displayRatings.map((rating) => (
            <div key={rating.value} className="flex flex-col items-center">
              <button
                type="button"
                onClick={() => handleResponseChange(rating.value)}
                className={`w-12 h-8 rounded border text-sm font-medium transition-colors ${
                  response === rating.value
                    ? 'bg-blue-500 text-white border-blue-500'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-100'
                }`}
              >
                {rating.value}
              </button>
              <span className="text-xs text-gray-600 mt-1 text-center max-w-16">
                {rating.label}
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderRadioOptions = () => {
    if (!question.options || !Array.isArray(question.options)) return null;

    return (
      <RadioGroup
        value={response?.toString()}
        onValueChange={(value) => handleResponseChange(value)}
        className="space-y-3"
      >
        {question.options.map((option, index) => (
          <div key={option.id || index} className="flex items-center space-x-3">
            <RadioGroupItem 
              value={option.value?.toString() || option.text || option} 
              id={`option-${option.id || index}`} 
            />
            <Label htmlFor={`option-${option.id || index}`} className="cursor-pointer">
              {option.text || option.label || option}
            </Label>
          </div>
        ))}
      </RadioGroup>
    );
  };

  const renderCheckboxOptions = () => {
    if (!question.options || !Array.isArray(question.options)) return null;

    const selectedValues = Array.isArray(response) ? response : [];

    return (
      <div className="space-y-3">
        {question.options.map((option, index) => (
          <div key={option.id || index} className="flex items-center space-x-3">
            <Checkbox
              id={`checkbox-${option.id || index}`}
              checked={selectedValues.includes(option.value || option.text || option)}
              onCheckedChange={(checked) => {
                const optionValue = option.value || option.text || option;
                const newValues = checked
                  ? [...selectedValues, optionValue]
                  : selectedValues.filter(v => v !== optionValue);
                handleResponseChange(newValues);
              }}
            />
            <Label htmlFor={`checkbox-${option.id || index}`} className="cursor-pointer">
              {option.text || option.label || option}
            </Label>
          </div>
        ))}
      </div>
    );
  };

  const renderTextarea = () => (
    <Textarea
      value={response}
      onChange={(e) => handleResponseChange(e.target.value)}
      placeholder="Enter your response..."
      className="min-h-[100px]"
    />
  );

  const renderInput = () => (
    <Input
      value={response}
      onChange={(e) => handleResponseChange(e.target.value)}
      placeholder="Enter your response..."
    />
  );

  const renderQuestionContent = () => {
    switch (question.resourcetype || question.type) {
      case 'QuestionRating':
        return renderRatingScale();
      case 'QuestionRadio':
        return renderRadioOptions();
      case 'QuestionCheckbox':
        return renderCheckboxOptions();
      case 'QuestionInput':
        return renderInput();
      case 'Paragraph':
        return renderTextarea();
      default:
        return (
          <div className="p-4 border border-dashed rounded-lg text-center text-muted-foreground">
            Question type "{question.resourcetype || question.type}" not yet implemented
          </div>
        );
    }
  };

  return (
    <div className="mb-6 border-b border-gray-200 pb-6">
      {/* Question Header */}
      <div className="mb-4">
        <h3 className="text-base font-medium text-gray-800">
          {questionNumber}. {question.title || question.label}
        </h3>
        {question.mandatory && (
          <span className="text-red-500 text-sm ml-1">*</span>
        )}
      </div>

      {/* Question Content */}
      <div className="mb-4">
        {renderQuestionContent()}
      </div>

      {/* Feedback Section */}
      {question.collect_feedback && (
        <div className="mt-4">
          {!showFeedback ? (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFeedback(true)}
              className="text-blue-600 border-blue-600 hover:bg-blue-50"
            >
              <MessageSquare className="h-4 w-4 mr-1" />
              Add Comment
            </Button>
          ) : (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor={`feedback-${question.id}`} className="text-sm font-medium text-gray-700">
                  Additional Comments (Optional)
                </Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowFeedback(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  Hide
                </Button>
              </div>
              <Textarea
                id={`feedback-${question.id}`}
                value={feedback}
                onChange={(e) => handleFeedbackChange(e.target.value)}
                placeholder="Share any additional thoughts or feedback..."
                className="min-h-[80px] border-gray-300"
              />
            </div>
          )}
        </div>
      )}

      {isSaving && (
        <div className="text-sm text-blue-600 mt-2">
          Saving...
        </div>
      )}
    </div>
  );
};

export default QuestionRenderer;
