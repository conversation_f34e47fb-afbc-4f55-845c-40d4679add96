import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@repo/ui/components/card';
import { Button } from '@repo/ui/components/button';
import { Textarea } from '@repo/ui/components/textarea';
import { Input } from '@repo/ui/components/input';
import { RadioGroup, RadioGroupItem } from '@repo/ui/components/radio-group';
import { Checkbox } from '@repo/ui/components/checkbox';
import { Label } from '@repo/ui/components/label';
import { Badge } from '@repo/ui/components/badge';
import { Separator } from '@repo/ui/components/separator';
import { MessageSquare, Star } from 'lucide-react';
import { SurveyQuestion } from '@/services/engagementSurveyService';

interface QuestionRendererProps {
  question: SurveyQuestion;
  questionNumber: number;
  onResponseUpdate: (questionId: number, value: any, responseId?: number) => void;
  onCommentUpdate: (questionId: number, comment: string, commentId?: number) => void;
  isSaving: boolean;
}

const QuestionRenderer: React.FC<QuestionRendererProps> = ({
  question,
  questionNumber,
  onResponseUpdate,
  onCommentUpdate,
  isSaving
}) => {
  const [response, setResponse] = useState(question.response?.value || '');
  const [feedback, setFeedback] = useState('');
  const [showFeedback, setShowFeedback] = useState(false);

  useEffect(() => {
    setResponse(question.response?.value || '');
  }, [question.response?.value]);

  const handleResponseChange = (newResponse: any) => {
    setResponse(newResponse);
    onResponseUpdate(question.id, newResponse, question.response?.id);
  };

  const handleFeedbackChange = (newFeedback: string) => {
    setFeedback(newFeedback);
    onCommentUpdate(question.id, newFeedback);
  };

  const renderRatingScale = () => {
    const ratings = [
      { value: 1, label: 'Strongly Disagree' },
      { value: 2, label: 'Disagree' },
      { value: 3, label: 'Neutral' },
      { value: 4, label: 'Agree' },
      { value: 5, label: 'Strongly Agree' },
    ];

    // If reverse scale is enabled, reverse the order
    const displayRatings = question.is_reverse_scale ? [...ratings].reverse() : ratings;

    return (
      <div className="space-y-4">
        <RadioGroup
          value={response?.toString()}
          onValueChange={(value) => handleResponseChange(parseInt(value))}
          className="space-y-3"
        >
          {displayRatings.map((rating) => (
            <div key={rating.value} className="flex items-center space-x-3">
              <RadioGroupItem value={rating.value.toString()} id={`rating-${rating.value}`} />
              <Label 
                htmlFor={`rating-${rating.value}`}
                className="flex items-center space-x-2 cursor-pointer"
              >
                <div className="flex">
                  {Array.from({ length: 5 }, (_, i) => (
                    <Star
                      key={i}
                      className={`h-4 w-4 ${
                        i < rating.value
                          ? 'fill-yellow-400 text-yellow-400'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <span>{rating.label}</span>
              </Label>
            </div>
          ))}
        </RadioGroup>
      </div>
    );
  };

  const renderRadioOptions = () => {
    if (!question.options || !Array.isArray(question.options)) return null;

    return (
      <RadioGroup
        value={response?.toString()}
        onValueChange={(value) => handleResponseChange(value)}
        className="space-y-3"
      >
        {question.options.map((option, index) => (
          <div key={option.id || index} className="flex items-center space-x-3">
            <RadioGroupItem 
              value={option.value?.toString() || option.text || option} 
              id={`option-${option.id || index}`} 
            />
            <Label htmlFor={`option-${option.id || index}`} className="cursor-pointer">
              {option.text || option.label || option}
            </Label>
          </div>
        ))}
      </RadioGroup>
    );
  };

  const renderCheckboxOptions = () => {
    if (!question.options || !Array.isArray(question.options)) return null;

    const selectedValues = Array.isArray(response) ? response : [];

    return (
      <div className="space-y-3">
        {question.options.map((option, index) => (
          <div key={option.id || index} className="flex items-center space-x-3">
            <Checkbox
              id={`checkbox-${option.id || index}`}
              checked={selectedValues.includes(option.value || option.text || option)}
              onCheckedChange={(checked) => {
                const optionValue = option.value || option.text || option;
                const newValues = checked
                  ? [...selectedValues, optionValue]
                  : selectedValues.filter(v => v !== optionValue);
                handleResponseChange(newValues);
              }}
            />
            <Label htmlFor={`checkbox-${option.id || index}`} className="cursor-pointer">
              {option.text || option.label || option}
            </Label>
          </div>
        ))}
      </div>
    );
  };

  const renderTextarea = () => (
    <Textarea
      value={response}
      onChange={(e) => handleResponseChange(e.target.value)}
      placeholder="Enter your response..."
      className="min-h-[100px]"
    />
  );

  const renderInput = () => (
    <Input
      value={response}
      onChange={(e) => handleResponseChange(e.target.value)}
      placeholder="Enter your response..."
    />
  );

  const renderQuestionContent = () => {
    switch (question.resourcetype || question.type) {
      case 'QuestionRating':
        return renderRatingScale();
      case 'QuestionRadio':
        return renderRadioOptions();
      case 'QuestionCheckbox':
        return renderCheckboxOptions();
      case 'QuestionInput':
        return renderInput();
      case 'Paragraph':
        return renderTextarea();
      default:
        return (
          <div className="p-4 border border-dashed rounded-lg text-center text-muted-foreground">
            Question type "{question.resourcetype || question.type}" not yet implemented
          </div>
        );
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg font-medium">
              <span className="text-blue-600 mr-2">{questionNumber}.</span>
              {question.title || question.label}
            </CardTitle>
            {question.mandatory && (
              <Badge variant="destructive" className="mt-2">
                Required
              </Badge>
            )}
          </div>
          {question.collect_feedback && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFeedback(!showFeedback)}
              className="ml-4"
            >
              <MessageSquare className="h-4 w-4 mr-1" />
              {showFeedback ? 'Hide' : 'Add'} Comment
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {renderQuestionContent()}
        
        {showFeedback && question.collect_feedback && (
          <>
            <Separator />
            <div className="space-y-2">
              <Label htmlFor={`feedback-${question.id}`} className="text-sm font-medium">
                Additional Comments (Optional)
              </Label>
              <Textarea
                id={`feedback-${question.id}`}
                value={feedback}
                onChange={(e) => handleFeedbackChange(e.target.value)}
                placeholder="Share any additional thoughts or feedback..."
                className="min-h-[80px]"
              />
            </div>
          </>
        )}
        
        {isSaving && (
          <div className="text-sm text-muted-foreground">
            Saving...
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default QuestionRenderer;
