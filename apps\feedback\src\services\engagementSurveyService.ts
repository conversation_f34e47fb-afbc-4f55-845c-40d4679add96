import axiosInstance from '@/lib/axios';

export interface SurveyResponse {
  id: string;
  survey: string;
  resourcetype: string;
}

export interface SurveyQuestion {
  id: number;
  title: string;
  label: string;
  type: string;
  resourcetype: string;
  section: number;
  mandatory: boolean;
  collect_feedback: boolean;
  is_reverse_scale: boolean;
  options?: any[];
  response?: {
    id: number;
    value: any;
    is_valid: boolean;
  };
}

export interface SurveySection {
  id: number;
  title: string;
  questions: SurveyQuestion[];
}

export interface SurveyMeta {
  title: string;
  endDate: string;
  canSubmit: boolean;
  nextTitle: string;
  buttonType: string;
  hideBack: boolean;
  hideNoBasisOption?: boolean;
}

export interface EngagementSurveyData {
  meta: SurveyMeta;
  sections: SurveySection[];
  questions: SurveyQuestion[];
  responseId: string;
  versionId: string;
  demographicsId?: string;
  completion: number;
}

/**
 * Get survey version ID for the given survey index
 */
export const getSurveyVersionId = async (surveyIndexId: string): Promise<{ versionId: string; demographicsId?: string }> => {
  const response = await axiosInstance.get(`/survey/survey-index/${surveyIndexId}`);
  const data = response.data;

  if (data.surveys && data.surveys.length) {
    const [version] = data.surveys;
    return {
      versionId: version.id,
      demographicsId: data.demographic_id,
    };
  }

  return {
    versionId: '',
    demographicsId: data.demographic_id,
  };
};

/**
 * Create a new survey response for engagement survey
 */
export const createSurveyResponse = async (surveyVersionId: string): Promise<SurveyResponse> => {
  const response = await axiosInstance.post('/survey/survey-response/', {
    survey: surveyVersionId,
    resourcetype: 'SurveyResponseEngagement',
  });
  
  return response.data;
};

/**
 * Get survey structure and questions
 */
export const getSurveyStructure = async (surveyVersionId: string) => {
  const response = await axiosInstance.get(`/survey/survey/${surveyVersionId}?survey=${surveyVersionId}`);
  return response.data;
};

/**
 * Get existing responses for a survey response (sections and components)
 */
export const getSurveyResponses = async (responseId: string) => {
  const response = await axiosInstance.get(`/survey/survey-response/${responseId}`);
  return response.data;
};

/**
 * Transform raw survey data into structured format
 */
export const transformSurveyData = (surveyData: any, responsesData: any, responseId: string, versionId: string, demographicsId?: string): EngagementSurveyData => {
  // The responsesData comes from /survey/survey-response/{id} and contains sections and components
  const { sections, components } = responsesData;
  const { questions } = surveyData;

  // Create a map of responses by question ID from components
  const responseMap = new Map();
  if (components && components.question_responses) {
    components.question_responses.forEach((response: any) => {
      responseMap.set(response.question, response);
    });
  }

  // Transform questions with their responses
  const transformedQuestions: SurveyQuestion[] = (questions || [])
    .filter((item: any) => item.resourcetype !== "QuestionNumber") // Filter out number questions
    .map((question: any) => {
      const response = responseMap.get(question.id);

      return {
        id: question.id,
        title: question.label,
        label: question.label,
        type: question.resourcetype,
        resourcetype: question.resourcetype,
        section: question.section,
        mandatory: question.mandatory,
        collect_feedback: question.collect_feedback,
        is_reverse_scale: question.is_reverse_scale,
        options: question.options || [],
        response: response ? {
          id: response.id,
          value: response.value,
          is_valid: response.is_valid,
        } : undefined,
      };
    });

  // Group questions by section
  const sectionMap = new Map();
  (sections || []).forEach((section: any) => {
    sectionMap.set(section.id, {
      id: section.id,
      title: section.title,
      questions: transformedQuestions.filter(q => q.section === section.id),
    });
  });

  const transformedSections = Array.from(sectionMap.values());

  // Calculate completion percentage
  const totalQuestions = transformedQuestions.length;
  const answeredQuestions = transformedQuestions.filter(q =>
    q.response?.value !== undefined &&
    q.response?.value !== null &&
    q.response?.value !== ''
  ).length;
  const completion = totalQuestions > 0 ? Math.round((answeredQuestions / totalQuestions) * 100) : 0;

  // Get survey metadata from responsesData
  const surveyMeta = responsesData.index || {};

  return {
    meta: {
      title: surveyMeta.title || 'Engagement Survey',
      endDate: surveyMeta.deadline || '',
      canSubmit: completion === 100,
      nextTitle: 'Next',
      buttonType: 'primary',
      hideBack: false,
      hideNoBasisOption: responsesData.hide_no_basis_option || false,
    },
    sections: transformedSections,
    questions: transformedQuestions,
    responseId,
    versionId,
    demographicsId,
    completion,
  };
};

/**
 * Get complete engagement survey data
 */
export const getEngagementSurveyData = async (surveyIndexId: string): Promise<EngagementSurveyData> => {
  try {
    // Step 1: Get survey version ID
    const { versionId, demographicsId } = await getSurveyVersionId(surveyIndexId);

    if (!versionId) {
      throw new Error('No Survey Version Present');
    }

    // Step 2: Create survey response
    const surveyResponse = await createSurveyResponse(versionId);

    // Step 3: Get survey structure and existing responses in parallel
    const [surveyStructure, existingResponses] = await Promise.all([
      getSurveyStructure(versionId),
      getSurveyResponses(surveyResponse.id),
    ]);

    // Step 4: Transform and return structured data
    return transformSurveyData(
      surveyStructure,
      existingResponses,
      surveyResponse.id,
      versionId,
      demographicsId
    );
  } catch (error) {
    console.error('Error fetching engagement survey data:', error);
    throw error;
  }
};

/**
 * Save or update a question response
 */
export const saveQuestionResponse = async (
  responseId: string,
  questionId: number,
  value: any,
  existingResponseId?: number
): Promise<any> => {
  const payload = {
    survey_response: responseId,
    question: questionId,
    value: value,
  };
  
  if (existingResponseId) {
    // Update existing response
    return axiosInstance.patch(`/survey/question-response/${existingResponseId}/`, payload);
  } else {
    // Create new response
    return axiosInstance.post('/survey/question-response/', payload);
  }
};

/**
 * Save or update a comment response
 */
export const saveCommentResponse = async (
  responseId: string,
  questionId: number,
  comment: string,
  existingCommentId?: number
): Promise<any> => {
  const payload = {
    survey_response: responseId,
    question: questionId,
    value: comment,
  };
  
  if (existingCommentId) {
    // Update existing comment
    return axiosInstance.patch(`/survey/comment-response/${existingCommentId}/`, payload);
  } else {
    // Create new comment
    return axiosInstance.post('/survey/comment-response/', payload);
  }
};

/**
 * Submit the complete survey
 */
export const submitSurvey = async (responseId: string): Promise<void> => {
  await axiosInstance.post(`/survey/survey-response/${responseId}/submit/`);
};
